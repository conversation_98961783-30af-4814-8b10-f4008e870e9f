<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="10">
				<el-button @click="showAddDialog" type="primary">添加</el-button>
			</el-col>
			<el-col :span="14" style="text-align: right; display: flex; align-items: center; justify-content: flex-end; gap: 10px;">
				<el-select
					v-model="search.status"
					placeholder="选择状态"
					style="width: 120px; margin-right: 10px;"
					clearable
					@change="refresh"
				>
					<el-option label="全部" value="" />
					<el-option label="待处理" :value="0" />
					<el-option label="已处理" :value="1" />
					<el-option label="已忽略" :value="2" />
				</el-select>
				<el-input
					placeholder="请输入URL或备注"
					v-model="search.s"
					style="width: 300px"
					clearable
					@clear="refresh"
				>
					<template #append>
						<el-button :icon="Search" @click="refresh" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column label="状态" width="100" align="center">
						<template #default="scope">
							<el-tag :type="getStatusType(scope.row.status)">
								{{ getStatusText(scope.row.status) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="Thread ID" width="120" align="center">
						<template #default="scope">
							{{ scope.row.threadID || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="URL" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							<el-link :href="scope.row.url" target="_blank" type="primary" v-if="scope.row.url">
								{{ scope.row.url }}
							</el-link>
							<span v-else>-</span>
						</template>
					</el-table-column>
					<el-table-column label="备注" width="200" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							{{ scope.row.remark || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="创建时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.createTime ? formatter(scope.row.createTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<!-- 添加/编辑弹出框 -->
		<el-dialog
			v-model="dialogVisible"
			:title="isEdit ? '编辑等待文章' : '添加等待文章'"
			width="600px"
			:close-on-click-modal="false"
			@close="closeDialog"
		>
			<div class="form-container">
				<el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
					<el-form-item label="Thread ID" :required="true" prop="threadID">
						<el-input v-model="form.threadID" placeholder="请输入Thread ID" />
					</el-form-item>

					<el-form-item label="URL" :required="true" prop="url">
						<el-input v-model="form.url" placeholder="请输入URL" />
					</el-form-item>

					<el-form-item label="状态" :required="true" prop="status">
						<el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
							<el-option label="待处理" :value="0" />
							<el-option label="已处理" :value="1" />
							<el-option label="已忽略" :value="2" />
						</el-select>
					</el-form-item>

					<el-form-item label="备注" prop="remark">
						<el-input
							type="textarea"
							v-model="form.remark"
							placeholder="请输入备注"
							rows="4"
						/>
					</el-form-item>
				</el-form>
			</div>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="closeDialog">取消</el-button>
					<el-button type="primary" @click="handleSubmit(formRef)">
						{{ isEdit ? '保存' : '提交' }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, Search } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import type { FormInstance } from "element-plus";

const { service } = useCool();

const search = reactive({
	s: "",
	status: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

// 弹出框相关数据
const dialogVisible = ref(false);
const isEdit = ref(false);

const form = reactive({
	id: 0,
	threadID: "",
	url: "",
	remark: "",
	status: 0
});

const rules = reactive({
	threadID: [{ required: true, message: "请输入Thread ID", trigger: "blur" }],
	url: [{ required: true, message: "请输入URL", trigger: "blur" }],
	status: [{ required: true, message: "请选择状态", trigger: "change" }]
});

const formRef = ref<FormInstance>();

const formatter = (datetime: any) => {
	if (!datetime) return '-';
	return datelineToDate(new Date(datetime).getTime() / 1000, "YYYY-MM-DD HH:mm:ss");
};

const getStatusType = (status: number) => {
	switch (status) {
		case 0: return 'warning';  // 待处理
		case 1: return 'success';  // 已处理
		case 2: return 'info';     // 已忽略
		default: return '';
	}
};

const getStatusText = (status: number) => {
	switch (status) {
		case 0: return '待处理';
		case 1: return '已处理';
		case 2: return '已忽略';
		default: return '未知';
	}
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};

const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const editClick = (row: any) => {
	showEditDialog(row.id);
};

const deleteHandler = (row: any) => {
	service.base.common.portal
		.waitingArticleDelete({
			id: row.id
		})
		.then(() => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.portal
		.waitingArticlePage({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 弹出框控制方法
const showAddDialog = () => {
	isEdit.value = false;
	resetForm();
	dialogVisible.value = true;
};

const showEditDialog = (id: number) => {
	isEdit.value = true;
	resetForm();
	form.id = id;
	dialogVisible.value = true;
	nextTick(() => {
		loadData();
	});
};

const closeDialog = () => {
	dialogVisible.value = false;
	resetForm();
};

const resetForm = () => {
	Object.assign(form, {
		id: 0,
		threadID: "",
		url: "",
		remark: "",
		status: 0
	});

	// 清除表单验证
	if (formRef.value) {
		formRef.value.clearValidate();
	}
};

// 加载编辑数据
const loadData = async () => {
	service.base.common.portal
		.waitingArticleFindOne({
			id: form.id
		})
		.then((res) => {
			Object.assign(form, res);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 表单提交
const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, _fields) => {
		if (!isValid) return;

		if (isEdit.value) {
			// 编辑模式
			service.base.common.portal
				.waitingArticleUpdate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已修改!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			// 添加模式
			service.base.common.portal
				.waitingArticleCreate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已添加!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";

.form-container {
	max-height: 70vh;
	overflow-y: auto;
	padding: 20px;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}
</style>