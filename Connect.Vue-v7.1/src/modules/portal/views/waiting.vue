<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="10">
				<el-button @click="showAddDialog" type="primary">添加</el-button>
			</el-col>
			<el-col :span="14" style="text-align: right; display: flex; align-items: center; justify-content: flex-end; gap: 10px;">
				<el-select
					v-model="search.status"
					placeholder="选择状态"
					style="width: 120px; margin-right: 10px;"
					clearable
					@change="refresh"
				>
					<el-option label="全部" value="" />
					<el-option label="待处理" :value="0" />
					<el-option label="已处理" :value="1" />
					<el-option label="已忽略" :value="2" />
				</el-select>
				<el-input
					placeholder="请输入URL或备注"
					v-model="search.s"
					style="width: 300px"
					clearable
					@clear="refresh"
				>
					<template #append>
						<el-button :icon="Search" @click="refresh" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column label="状态" width="100" align="center">
						<template #default="scope">
							<el-tag :type="getStatusType(scope.row.status)">
								{{ getStatusText(scope.row.status) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="Thread ID" width="120" align="center">
						<template #default="scope">
							{{ scope.row.threadID || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="URL" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							<el-link :href="scope.row.url" target="_blank" type="primary" v-if="scope.row.url">
								{{ scope.row.url }}
							</el-link>
							<span v-else>-</span>
						</template>
					</el-table-column>
					<el-table-column label="备注" width="200" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							{{ scope.row.remark || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="创建时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.createTime ? formatter(scope.row.createTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<!-- 添加/编辑弹出框 -->
		<el-dialog
			v-model="dialogVisible"
			:title="isEdit ? '编辑等待文章' : '添加等待文章'"
			width="90%"
			:close-on-click-modal="false"
			@close="closeDialog"
			class="waiting-dialog"
		>
			<div class="waiting-editor-container">
				<!-- 左侧编辑区域 -->
				<div class="editor-left">
					<div class="editor-header">
						<h3>等待文章信息编辑</h3>
					</div>
					<div class="form-container">
						<el-form :model="form" label-width="100px" :rules="rules" ref="formRef" size="small">
							<el-form-item label="Thread ID" :required="true" prop="threadID">
								<el-input v-model="form.threadID" placeholder="请输入Thread ID" @input="updatePreview" />
							</el-form-item>

							<el-form-item label="URL" :required="true" prop="url">
								<el-input v-model="form.url" placeholder="请输入URL" @input="updatePreview" />
							</el-form-item>

							<el-form-item label="状态" :required="true" prop="status">
								<el-select v-model="form.status" placeholder="请选择状态" style="width: 100%" @change="updatePreview">
									<el-option label="待处理" :value="0" />
									<el-option label="已处理" :value="1" />
									<el-option label="已忽略" :value="2" />
								</el-select>
							</el-form-item>

							<el-form-item label="备注" prop="remark">
								<el-input
									type="textarea"
									v-model="form.remark"
									placeholder="请输入备注"
									:rows="8"
									@input="updatePreview"
								/>
							</el-form-item>
						</el-form>
					</div>
				</div>

				<!-- 右侧预览区域 -->
				<div class="editor-right">
					<div class="preview-header">
						<h3>文章信息预览</h3>
						<div class="preview-info">
							<span v-if="form.threadID">Thread ID：{{ form.threadID }}</span>
							<span>状态：{{ getStatusText(form.status) }}</span>
						</div>
					</div>
					<div class="preview-content">
						<div class="preview-meta">
							<div class="meta-item" v-if="form.url">
								<strong>URL:</strong>
								<el-link :href="form.url" target="_blank" type="primary">{{ form.url }}</el-link>
							</div>
							<div class="meta-item">
								<strong>状态:</strong>
								<el-tag :type="getStatusType(form.status)">
									{{ getStatusText(form.status) }}
								</el-tag>
							</div>
							<div class="meta-item" v-if="form.threadID">
								<strong>Thread ID:</strong> {{ form.threadID }}
							</div>
						</div>
						<div class="remark-preview" v-if="form.remark">
							<h4>备注预览:</h4>
							<div class="remark-display">{{ form.remark }}</div>
						</div>
						<div class="empty-preview" v-else>
							<p>暂无备注内容</p>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog">取消</el-button>
					<el-button type="primary" @click="handleSubmit(formRef)">
						{{ isEdit ? '保存' : '提交' }}
					</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, Search } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import type { FormInstance } from "element-plus";

const { service } = useCool();

const search = reactive({
	s: "",
	status: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

// 弹出框相关数据
const dialogVisible = ref(false);
const isEdit = ref(false);

const form = reactive({
	id: 0,
	threadID: "",
	url: "",
	remark: "",
	status: 0
});

const rules = reactive({
	threadID: [{ required: true, message: "请输入Thread ID", trigger: "blur" }],
	url: [{ required: true, message: "请输入URL", trigger: "blur" }],
	status: [{ required: true, message: "请选择状态", trigger: "change" }]
});

const formRef = ref<FormInstance>();

const formatter = (datetime: any) => {
	if (!datetime) return '-';
	return datelineToDate(new Date(datetime).getTime() / 1000, "YYYY-MM-DD HH:mm:ss");
};

const getStatusType = (status: number) => {
	switch (status) {
		case 0: return 'warning';  // 待处理
		case 1: return 'success';  // 已处理
		case 2: return 'info';     // 已忽略
		default: return '';
	}
};

const getStatusText = (status: number) => {
	switch (status) {
		case 0: return '待处理';
		case 1: return '已处理';
		case 2: return '已忽略';
		default: return '未知';
	}
};

// 更新预览内容（等待文章相对简单，主要是实时更新显示）
const updatePreview = () => {
	// 等待文章的预览主要是实时显示表单内容，不需要特殊处理
	// 这里可以添加一些实时验证逻辑
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};

const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const editClick = (row: any) => {
	showEditDialog(row.id);
};

const deleteHandler = (row: any) => {
	service.base.common.portal
		.waitingArticleDelete({
			id: row.id
		})
		.then(() => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.portal
		.waitingArticlePage({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 弹出框控制方法
const showAddDialog = () => {
	isEdit.value = false;
	resetForm();
	dialogVisible.value = true;
};

const showEditDialog = (id: number) => {
	isEdit.value = true;
	resetForm();
	form.id = id;
	dialogVisible.value = true;
	nextTick(() => {
		loadData();
	});
};

const closeDialog = () => {
	dialogVisible.value = false;
	resetForm();
};

const resetForm = () => {
	Object.assign(form, {
		id: 0,
		threadID: "",
		url: "",
		remark: "",
		status: 0
	});

	// 清除表单验证
	if (formRef.value) {
		formRef.value.clearValidate();
	}
};

// 加载编辑数据
const loadData = async () => {
	service.base.common.portal
		.waitingArticleFindOne({
			id: form.id
		})
		.then((res) => {
			Object.assign(form, res);
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 表单提交
const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, _fields) => {
		if (!isValid) return;

		if (isEdit.value) {
			// 编辑模式
			service.base.common.portal
				.waitingArticleUpdate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已修改!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			// 添加模式
			service.base.common.portal
				.waitingArticleCreate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已添加!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";

// 等待文章编辑器样式
.waiting-dialog {
	:deep(.el-dialog) {
		margin: 20px auto;
		max-height: calc(100vh - 40px);
	}

	:deep(.el-dialog__body) {
		padding: 0;
		height: calc(100vh - 200px);
	}
}

.waiting-editor-container {
	display: flex;
	height: 100%;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	overflow: hidden;
}

.editor-left {
	width: 50%;
	border-right: 1px solid #e4e7ed;
	display: flex;
	flex-direction: column;
}

.editor-right {
	width: 50%;
	display: flex;
	flex-direction: column;
	background-color: #fafafa;
}

.editor-header, .preview-header {
	padding: 15px 20px;
	background-color: #f5f7fa;
	border-bottom: 1px solid #e4e7ed;

	h3 {
		margin: 0;
		font-size: 16px;
		color: #303133;
	}
}

.preview-header {
	.preview-info {
		margin-top: 8px;
		font-size: 12px;
		color: #909399;

		span {
			margin-right: 15px;
		}
	}
}

.form-container {
	flex: 1;
	overflow-y: auto;
	padding: 20px;
}

.preview-content {
	flex: 1;
	overflow-y: auto;
	padding: 20px;
}

.preview-meta {
	margin-bottom: 20px;

	.meta-item {
		margin-bottom: 12px;
		font-size: 14px;
		line-height: 1.5;

		strong {
			color: #606266;
			margin-right: 8px;
		}
	}
}

.remark-preview {
	margin-bottom: 20px;

	h4 {
		margin: 0 0 10px 0;
		font-size: 14px;
		color: #303133;
		border-bottom: 1px solid #e4e7ed;
		padding-bottom: 5px;
	}
}

.remark-display {
	background-color: #fff;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	padding: 15px;
	min-height: 100px;
	font-size: 14px;
	line-height: 1.6;
	color: #606266;
	white-space: pre-wrap;
}

.empty-preview {
	text-align: center;
	color: #c0c4cc;
	font-style: italic;
	margin-top: 50px;
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	padding: 15px 20px;
	border-top: 1px solid #e4e7ed;
	background-color: #fff;
}

// 响应式设计
@media (max-width: 1200px) {
	.waiting-editor-container {
		flex-direction: column;
	}

	.editor-left, .editor-right {
		width: 100%;
	}

	.editor-left {
		border-right: none;
		border-bottom: 1px solid #e4e7ed;
	}
}
</style>