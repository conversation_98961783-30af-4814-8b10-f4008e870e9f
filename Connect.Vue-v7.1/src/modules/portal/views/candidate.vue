<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="10">
				<el-button @click="showAddDialog" type="primary">添加</el-button>
			</el-col>
			<el-col :span="14" style="text-align: right; display: flex; align-items: center; justify-content: flex-end; gap: 10px;">
				<el-input
					placeholder="请输入标题或URL"
					v-model="search.s"
					style="width: 300px"
					clearable
					@clear="refresh"
				>
					<template #append>
						<el-button :icon="Search" @click="refresh" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column label="审核" width="80" align="center">
						<template #default="scope">
							<el-tag :type="scope.row.approvedBy ? 'success' : 'warning'">
								{{ scope.row.approvedBy ? '已审核' : '待审核' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="URL" width="200" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							<el-link :href="scope.row.url" target="_blank" type="primary" v-if="scope.row.url">
								{{ scope.row.url }}
							</el-link>
							<span v-else>-</span>
						</template>
					</el-table-column>
					<el-table-column label="标题" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							{{ scope.row.title || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="作者" width="100" align="center">
						<template #default="scope">
							{{ scope.row.author || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="频道" width="120" align="center">
						<template #default="scope">
							{{ scope.row.channel || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="标签" width="120" align="center">
						<template #default="scope">
							{{ scope.row.tag || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="添加人" width="100" align="center">
						<template #default="scope">
							{{ scope.row.addedBy || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="审核人" width="100" align="center">
						<template #default="scope">
							{{ scope.row.approvedBy || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="审核时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.approvedTime ? formatter(scope.row.approvedTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column label="创建时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.createTime ? formatter(scope.row.createTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<!-- 添加/编辑弹出框 -->
		<el-dialog
			v-model="dialogVisible"
			:title="isEdit ? '编辑候选文章' : '添加候选文章'"
			width="95%"
			:close-on-click-modal="false"
			@close="closeDialog"
			class="article-dialog"
		>
			<div class="article-editor-container">
				<!-- 左侧编辑区域 -->
				<div class="editor-left">
					<div class="editor-header">
						<h3>文章信息编辑</h3>
					</div>
					<div class="form-container">
						<el-form :model="form" label-width="80px" :rules="rules" ref="formRef" size="small">
							<el-form-item label="URL" :required="true" prop="url">
								<el-input v-model="form.url" placeholder="请输入URL" />
							</el-form-item>

							<el-form-item label="标题" :required="true" prop="title">
								<el-input v-model="form.title" placeholder="请输入标题" />
							</el-form-item>

							<el-row :gutter="20">
								<el-col :span="12">
									<el-form-item label="版块" prop="board">
										<el-input v-model="form.board" placeholder="请输入版块" />
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="作者" prop="author">
										<el-input v-model="form.author" placeholder="请输入作者" />
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="20">
								<el-col :span="12">
									<el-form-item label="频道" prop="channel">
										<el-input v-model="form.channel" placeholder="请输入频道" />
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="标签" prop="tag">
										<el-input v-model="form.tag" placeholder="请输入标签" />
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="20">
								<el-col :span="12">
									<el-form-item label="添加人" prop="addedBy">
										<el-input v-model="form.addedBy" placeholder="请输入添加人" />
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="审核人" prop="approvedBy">
										<el-input v-model="form.approvedBy" placeholder="请输入审核人" />
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="20">
								<el-col :span="12">
									<el-form-item label="WWW文章ID" prop="wwwArticleID">
										<el-input-number v-model="form.wwwArticleID" :min="0" style="width: 100%" />
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="WAID" prop="waid">
										<el-input v-model="form.waid" placeholder="请输入WAID" />
									</el-form-item>
								</el-col>
							</el-row>

							<el-form-item label="修复新闻" prop="fixNews">
								<el-checkbox v-model="form.fixNews" />
							</el-form-item>

							<el-form-item label="内容" prop="content">
								<el-input
									type="textarea"
									v-model="form.content"
									placeholder="请输入内容"
									:rows="8"
									@input="updatePreview"
								/>
							</el-form-item>

							<el-form-item label="参考内容" prop="refContent">
								<el-input
									type="textarea"
									v-model="form.refContent"
									placeholder="请输入参考内容"
									:rows="4"
								/>
							</el-form-item>
						</el-form>
					</div>
				</div>

				<!-- 右侧预览区域 -->
				<div class="editor-right">
					<div class="preview-header">
						<h3>文章预览</h3>
						<div class="preview-info">
							<span v-if="form.title">标题：{{ form.title }}</span>
							<span v-if="form.author">作者：{{ form.author }}</span>
						</div>
					</div>
					<div class="preview-content">
						<div class="preview-meta">
							<div class="meta-item" v-if="form.url">
								<strong>URL:</strong>
								<el-link :href="form.url" target="_blank" type="primary">{{ form.url }}</el-link>
							</div>
							<div class="meta-item" v-if="form.channel">
								<strong>频道:</strong> {{ form.channel }}
							</div>
							<div class="meta-item" v-if="form.tag">
								<strong>标签:</strong> {{ form.tag }}
							</div>
							<div class="meta-item" v-if="form.board">
								<strong>版块:</strong> {{ form.board }}
							</div>
						</div>
						<div class="content-preview">
							<h4>内容预览:</h4>
							<div class="content-display" v-html="previewContent"></div>
						</div>
						<div class="ref-content-preview" v-if="form.refContent">
							<h4>参考内容:</h4>
							<div class="ref-content-display">{{ form.refContent }}</div>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog">取消</el-button>
					<el-button type="primary" @click="handleSubmit(formRef)">
						{{ isEdit ? '保存' : '提交' }}
					</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { Edit, Delete, Search } from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import type { FormInstance } from "element-plus";

const { service } = useCool();

const search = reactive({
	s: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

// 弹出框相关数据
const dialogVisible = ref(false);
const isEdit = ref(false);

const form = reactive({
	id: 0,
	url: "",
	title: "",
	board: "",
	author: "",
	channel: "",
	tag: "",
	content: "",
	refContent: "",
	addedBy: "",
	approvedBy: "",
	wwwArticleID: null,
	fixNews: false,
	waid: ""
});

const rules = reactive({
	url: [{ required: true, message: "请输入URL", trigger: "blur" }],
	title: [{ required: true, message: "请输入标题", trigger: "blur" }]
});

const formRef = ref<FormInstance>();

// 预览相关
const previewContent = ref('');

const formatter = (datetime: any) => {
	if (!datetime) return '-';
	return datelineToDate(new Date(datetime).getTime() / 1000, "YYYY-MM-DD HH:mm:ss");
};

// 更新预览内容
const updatePreview = () => {
	// 简单的HTML内容预览，将换行转换为<br>
	previewContent.value = form.content
		.replace(/\n/g, '<br>')
		.replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};

const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const editClick = (row: any) => {
	showEditDialog(row.id);
};

const deleteHandler = (row: any) => {
	service.base.common.portal
		.candidateArticleDelete({
			id: row.id
		})
		.then(() => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.portal
		.candidateArticlePage({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 弹出框控制方法
const showAddDialog = () => {
	isEdit.value = false;
	resetForm();
	dialogVisible.value = true;
};

const showEditDialog = (id: number) => {
	isEdit.value = true;
	resetForm();
	form.id = id;
	dialogVisible.value = true;
	nextTick(() => {
		loadData();
	});
};

const closeDialog = () => {
	dialogVisible.value = false;
	resetForm();
};

const resetForm = () => {
	Object.assign(form, {
		id: 0,
		url: "",
		title: "",
		board: "",
		author: "",
		channel: "",
		tag: "",
		content: "",
		refContent: "",
		addedBy: "",
		approvedBy: "",
		wwwArticleID: null,
		fixNews: false,
		waid: ""
	});

	// 清除表单验证
	if (formRef.value) {
		formRef.value.clearValidate();
	}
};

// 加载编辑数据
const loadData = async () => {
	service.base.common.portal
		.candidateArticleFindOne({
			id: form.id
		})
		.then((res) => {
			Object.assign(form, res);
			updatePreview(); // 加载数据后更新预览
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 表单提交
const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, _fields) => {
		if (!isValid) return;

		if (isEdit.value) {
			// 编辑模式
			service.base.common.portal
				.candidateArticleUpdate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已修改!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			// 添加模式
			service.base.common.portal
				.candidateArticleCreate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已添加!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";

// 文章编辑器样式
.article-dialog {
	:deep(.el-dialog) {
		margin: 20px auto;
		max-height: calc(100vh - 40px);
	}

	:deep(.el-dialog__body) {
		padding: 0;
		height: calc(100vh - 200px);
	}
}

.article-editor-container {
	display: flex;
	height: 100%;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	overflow: hidden;
}

.editor-left {
	width: 50%;
	border-right: 1px solid #e4e7ed;
	display: flex;
	flex-direction: column;
}

.editor-right {
	width: 50%;
	display: flex;
	flex-direction: column;
	background-color: #fafafa;
}

.editor-header, .preview-header {
	padding: 15px 20px;
	background-color: #f5f7fa;
	border-bottom: 1px solid #e4e7ed;

	h3 {
		margin: 0;
		font-size: 16px;
		color: #303133;
	}
}

.preview-header {
	.preview-info {
		margin-top: 8px;
		font-size: 12px;
		color: #909399;

		span {
			margin-right: 15px;
		}
	}
}

.form-container {
	flex: 1;
	overflow-y: auto;
	padding: 20px;
}

.preview-content {
	flex: 1;
	overflow-y: auto;
	padding: 20px;
}

.preview-meta {
	margin-bottom: 20px;

	.meta-item {
		margin-bottom: 8px;
		font-size: 14px;
		line-height: 1.5;

		strong {
			color: #606266;
			margin-right: 8px;
		}
	}
}

.content-preview, .ref-content-preview {
	margin-bottom: 20px;

	h4 {
		margin: 0 0 10px 0;
		font-size: 14px;
		color: #303133;
		border-bottom: 1px solid #e4e7ed;
		padding-bottom: 5px;
	}
}

.content-display, .ref-content-display {
	background-color: #fff;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	padding: 15px;
	min-height: 100px;
	font-size: 14px;
	line-height: 1.6;
	color: #606266;
	white-space: pre-wrap;
}

.content-display {
	:deep(br) {
		line-height: 1.6;
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	padding: 15px 20px;
	border-top: 1px solid #e4e7ed;
	background-color: #fff;
}

// 响应式设计
@media (max-width: 1200px) {
	.article-editor-container {
		flex-direction: column;
	}

	.editor-left, .editor-right {
		width: 100%;
	}

	.editor-left {
		border-right: none;
		border-bottom: 1px solid #e4e7ed;
	}
}
</style>